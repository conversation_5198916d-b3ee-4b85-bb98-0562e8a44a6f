import { useState, useEffect, useMemo, useCallback } from "react";
import {
  A<PERSON><PERSON>ead<PERSON>,
  Landing<PERSON>ero,
  FilterTabs,
  StatsBar,
  OfferCard,
  OfferCardSkeleton,
  EmptyState,
  SearchAndSort,
} from "@/components/";
import { useToast } from "@/hooks/use-toast";
import { useOffers, type Offer } from "@/hooks/useOffers";
import { useConfig } from "@/hooks/useConfig";
import { STORAGE_KEYS } from "@/constants/storage/keys";

const Index = () => {
  const { offers, isLoading, refreshOffers } = useOffers();
  const { config } = useConfig();
  const [filteredOffers, setFilteredOffers] = useState<Offer[]>([]);
  const [activeFilter, setActiveFilter] = useState<string>("all");
  const [balance] = useState<number>(1250);
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [sortBy, setSortBy] = useState<string>("newest");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 10000]);
  const [validityFilter, setValidityFilter] = useState<number>(0);
  const [showLanding, setShowLanding] = useState<boolean>(!localStorage.getItem(STORAGE_KEYS.LAST_VISIT));
  const [showAdvancedFilters, setShowAdvancedFilters] = useState<boolean>(false);

  const processedOffers = useMemo(() => {
    let filtered = [...offers];

    if (activeFilter !== "all") {
      filtered = filtered.filter((offer) => offer.operator === activeFilter);
    }

    if (searchTerm) {
      const searchTermLower = searchTerm.toLowerCase();
    filtered = filtered.filter(
      (offer) =>
          offer.title.toLowerCase().includes(searchTermLower) ||
          offer.operator.toLowerCase().includes(searchTermLower)
    );
    }

    if (categoryFilter !== "all") {
      filtered = filtered.filter((offer) => offer.category === categoryFilter);
    }

    filtered = filtered.filter(
      (offer) =>
        offer.selling_price >= priceRange[0] &&
        offer.selling_price <= priceRange[1]
  );

    if (validityFilter > 0) {
      filtered = filtered.filter(
        (offer) => offer.validity_days >= validityFilter
    );
    }

    filtered.sort((a, b) => {
      switch (sortBy) {
        case "price-low":
          return a.selling_price - b.selling_price;
        case "price-high":
          return b.selling_price - a.selling_price;
        case "validity-high":
          return b.validity_days - a.validity_days;
        case "validity-low":
          return a.validity_days - b.validity_days;
        case "data-high": {
          const aData = parseFloat(a.data_amount.replace(/[^0-9.]/g, "")) || 0;
          const bData = parseFloat(b.data_amount.replace(/[^0-9.]/g, "")) || 0;
          return bData - aData;
        }
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });

    return filtered;
  }, [
    offers,
    activeFilter,
    searchTerm,
    categoryFilter,
    priceRange,
    validityFilter,
    sortBy,
  ]);

  useEffect(() => {
    setFilteredOffers(processedOffers);
  }, [processedOffers]);

  const offerCounts = useMemo(
    () => ({
      all: offers.length,
      GP: offers.filter((o) => o.operator === "GP").length,
      Robi: offers.filter((o) => o.operator === "Robi").length,
      Banglalink: offers.filter((o) => o.operator === "Banglalink").length,
      Airtel: offers.filter((o) => o.operator === "Airtel").length,
      Skitto: offers.filter((o) => o.operator === "Skitto").length,
    }),
    [offers]
  );

  const handleWhatsAppOrder = useCallback(
    (offer: Offer) => {
      const message = encodeURIComponent(
        `আসসালামু আলাইকুম! আমি এই অফারটি নিতে চাই:\n\n${offer.title}\n📱 অপারেটর: ${offer.operator}\n💰 দাম: ৳${offer.selling_price}\n\nঅনুগ্রহ করে আমাকে পরবর্তী ধাপ জানান।`
  );
      window.open(
        `https://wa.me/${offer.whatsapp_number}?text=${message}`,
        "_blank"
      );

      toast({
        title: "WhatsApp Opened",
        description: "Continue your order on WhatsApp",
      });
    },
    [toast]
  );

  const handlePhoneOrder = useCallback(
    (offer: Offer) => {
      window.open(`tel:+${offer.whatsapp_number}`);

      toast({
        title: "Calling...",
        description: `Connecting to ${offer.operator} sales team`,
      });
    },
    [toast]
  );

  const handleFilterChange = useCallback((filter: string) => {
    setActiveFilter(filter);
  }, []);

  const handleRefresh = useCallback(() => {
    refreshOffers();
    toast({
      title: "Refreshed",
      description: "Offers updated successfully",
    });
  }, [refreshOffers, toast]);

  const avgSavings = useMemo(() => {
    if (offers.length === 0) return 150;
    const totalSavings = offers.reduce((sum, offer) => {
      const savings = (offer.original_price || offer.selling_price * 1.2) - offer.selling_price;
      return sum + savings;
    }, 0);
    return Math.round(totalSavings / offers.length);
  }, [offers]);

    const handleGetStarted = useCallback(() => {
    localStorage.setItem(STORAGE_KEYS.LAST_VISIT, "true");
    setShowLanding(false);
  }, []);

  if (showLanding && !isLoading) {
    return (
      <LandingHero
        onGetStarted={handleGetStarted}
        totalOffers={offers.length}
        avgSavings={avgSavings}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <AppHeader
        balance={balance}
        onHomeClick={() => setShowLanding(true)}
      />

      <div className="px-3 py-2 space-y-3">
        {/* Search and Sort */}
        <SearchAndSort
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          sortBy={sortBy}
          onSortChange={setSortBy}
          categoryFilter={categoryFilter}
          onCategoryChange={setCategoryFilter}
          priceRange={priceRange}
          onPriceRangeChange={setPriceRange}
          validityFilter={validityFilter}
          onValidityFilterChange={setValidityFilter}
          offers={filteredOffers}
          showAdvanced={showAdvancedFilters}
          onToggleAdvanced={() => setShowAdvancedFilters(!showAdvancedFilters)}
        />

        {/* Filter Tabs */}
        <FilterTabs
          activeFilter={activeFilter}
          onFilterChange={handleFilterChange}
          offerCounts={offerCounts}
        />

        {/* Stats Bar */}
        <StatsBar availableOffers={filteredOffers.length} />

        {/* Main Content */}
        <main className="space-y-3 pb-6">
          {isLoading ? (
            [...Array(6)].map((_, index) => (
              <OfferCardSkeleton key={index} />
            ))
          ) : filteredOffers.length === 0 ? (
            <EmptyState
              title="No offers found"
              description="Try selecting a different operator or check back later"
              showRefresh={true}
              onRefresh={handleRefresh}
            />
          ) : (
            <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2">
              {filteredOffers.map((offer) => (
                <OfferCard
                  key={offer.id}
                  offer={offer}
                  onWhatsAppOrder={() => handleWhatsAppOrder(offer)}
                  onPhoneOrder={() => handlePhoneOrder(offer)}
                />
              ))}
            </div>
          )}
        </main>

        <footer className="px-4 py-6 text-center border-t border-border mt-8">
          <p className="body-sm text-muted">
            © 2024 {config.company_name || "SUPERDEALS"}। All rights reserved.
          </p>
          <p className="body-sm text-muted mt-1">
            📞 Support: {config.support_phone || "+8801640167593"} | 📧{" "}
            {config.support_email || "<EMAIL>"}
          </p>
        </footer>
      </div>
    </div>
  );
};
export default Index;