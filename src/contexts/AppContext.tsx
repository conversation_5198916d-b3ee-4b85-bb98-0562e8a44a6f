import React, { createContext, useContext, useReducer, ReactNode } from 'react';

// State interface
interface AppState {
  favorites: string[];
  comparison: string[];
  filters: {
    operator: string;
    category: string;
    priceRange: [number, number];
    validityFilter: string;
    searchQuery: string;
    sortBy: string;
  };
  ui: {
    showFavoritesModal: boolean;
    showComparisonModal: boolean;
  };
}

// Action types
type AppAction =
  | { type: 'ADD_FAVORITE'; payload: string }
  | { type: 'REMOVE_FAVORITE'; payload: string }
  | { type: 'ADD_TO_COMPARISON'; payload: string }
  | { type: 'REMOVE_FROM_COMPARISON'; payload: string }
  | { type: 'CLEAR_COMPARISON' }
  | { type: 'SET_FILTER'; payload: { key: keyof AppState['filters']; value: any } }
  | { type: 'RESET_FILTERS' }
  | { type: 'TOGGLE_FAVORITES_MODAL' }
  | { type: 'TOGGLE_COMPARISON_MODAL' }
  | { type: 'SET_UI'; payload: { key: keyof AppState['ui']; value: boolean } };

// Initial state
const initialState: AppState = {
  favorites: [],
  comparison: [],
  filters: {
    operator: 'all',
    category: 'all',
    priceRange: [0, 2000],
    validityFilter: 'all',
    searchQuery: '',
    sortBy: 'newest',
  },
  ui: {
    showFavoritesModal: false,
    showComparisonModal: false,
  },
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'ADD_FAVORITE':
      return {
        ...state,
        favorites: state.favorites.includes(action.payload)
          ? state.favorites
          : [...state.favorites, action.payload],
      };
    case 'REMOVE_FAVORITE':
      return {
        ...state,
        favorites: state.favorites.filter(id => id !== action.payload),
      };
    case 'ADD_TO_COMPARISON':
      return {
        ...state,
        comparison: state.comparison.includes(action.payload) || state.comparison.length >= 3
          ? state.comparison
          : [...state.comparison, action.payload],
      };
    case 'REMOVE_FROM_COMPARISON':
      return {
        ...state,
        comparison: state.comparison.filter(id => id !== action.payload),
      };
    case 'CLEAR_COMPARISON':
      return {
        ...state,
        comparison: [],
      };
    case 'SET_FILTER':
      return {
        ...state,
        filters: {
          ...state.filters,
          [action.payload.key]: action.payload.value,
        },
      };
    case 'RESET_FILTERS':
      return {
        ...state,
        filters: initialState.filters,
      };
    case 'TOGGLE_FAVORITES_MODAL':
      return {
        ...state,
        ui: {
          ...state.ui,
          showFavoritesModal: !state.ui.showFavoritesModal,
        },
      };
    case 'TOGGLE_COMPARISON_MODAL':
      return {
        ...state,
        ui: {
          ...state.ui,
          showComparisonModal: !state.ui.showComparisonModal,
        },
      };
    case 'SET_UI':
      return {
        ...state,
        ui: {
          ...state.ui,
          [action.payload.key]: action.payload.value,
        },
      };
    default:
      return state;
  }
}

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

// Provider component
export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

// Hook to use the context
export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}
