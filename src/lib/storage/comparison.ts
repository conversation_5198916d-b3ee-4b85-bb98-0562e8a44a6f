// import { STORAGE_KEYS } from "@/constants";

// // Comparison utility functions
// export const getComparison = (): string[] => {
//   const comparison = localStorage.getItem(STORAGE_KEYS.COMPARISON);
//   return comparison ? JSON.parse(comparison) : [];
// };

// export const addToComparison = (offerId: string): void => {
//   const comparison = getComparison();
//   if (!comparison.includes(offerId) && comparison.length < 3) {
//     comparison.push(offerId);
//     localStorage.setItem(STORAGE_KEYS.COMPARISON, JSON.stringify(comparison));
//   }
// };

// export const removeFromComparison = (offerId: string): void => {
//   const comparison = getComparison();
//   const updated = comparison.filter((id) => id !== offerId);
//   localStorage.setItem(STORAGE_KEYS.COMPARISON, JSON.stringify(updated));
// };

// export const clearComparison = (): void => {
//   localStorage.removeItem(STORAGE_KEYS.COMPARISON);
// };

// export const isInComparison = (offerId: string): boolean => {
//   return getComparison().includes(offerId);
// };
