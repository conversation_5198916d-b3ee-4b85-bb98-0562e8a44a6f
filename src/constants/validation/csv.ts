// CSV Upload Constants
export const CSV_HEADERS = {
  REQUIRED: [
    "operator",
    "title", 
    "data_amount",
    "selling_price",
    "validity_days"
  ],
  OPTIONAL: [
    "minutes",
    "original_price",
    "region",
    "category",
    "whatsapp_number",
    "description"
  ],
  ALL: [
    "operator",
    "title",
    "data_amount", 
    "minutes",
    "validity_days",
    "selling_price",
    "original_price",
    "region",
    "category",
    "whatsapp_number",
    "description"
  ]
} as const;

// CSV Template for Download
export const CSV_TEMPLATE = {
  headers: CSV_HEADERS.ALL,
  sampleData: [
    {
      operator: "GP",
      title: "50GB + 1500 Minutes Bundle",
      data_amount: "50GB",
      minutes: 1500,
      validity_days: 30,
      selling_price: 775,
      original_price: 900,
      region: "All Bangladesh",
      category: "combo",
      whatsapp_number: "+8801712345678",
      description: "High-speed data with unlimited minutes"
    }
  ]
} as const;

// Validation Rules
export const VALIDATION = {
  TITLE: {
    MIN_LENGTH: 5,
    MAX_LENGTH: 100,
  },
  DATA_AMOUNT: {
    PATTERN: /^\d+(\.\d+)?(MB|GB|TB)$/i,
  },
  PRICE: {
    MIN: 1,
    MAX: 10000,
  },
  VALIDITY: {
    MIN: 1,
    MAX: 365,
  },
  MINUTES: {
    MIN: 0,
    MAX: 50000,
  },
  WHATSAPP: {
    PATTERN: /^\+8801[3-9]\d{8}$/,
  }
} as const;
