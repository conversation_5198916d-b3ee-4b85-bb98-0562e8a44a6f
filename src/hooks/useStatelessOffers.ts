import { useMemo } from 'react';
import { useOffers } from './useOffers';
import { useAppContext } from '@/contexts/AppContext';
import { type Offer } from '@/types';

export function useStatelessOffers() {
  const { offers, loading, error, refetch } = useOffers();
  const { state } = useAppContext();

  // Filter and sort offers based on current state
  const filteredOffers = useMemo(() => {
    if (!offers) return [];

    let filtered = offers.filter(offer => {
      // Operator filter
      if (state.filters.operator !== 'all' && offer.operator !== state.filters.operator) {
        return false;
      }

      // Category filter
      if (state.filters.category !== 'all' && offer.category !== state.filters.category) {
        return false;
      }

      // Price range filter
      const [minPrice, maxPrice] = state.filters.priceRange;
      if (offer.selling_price < minPrice || offer.selling_price > maxPrice) {
        return false;
      }

      // Validity filter
      if (state.filters.validityFilter !== 'all') {
        const validity = offer.validity_days;
        switch (state.filters.validityFilter) {
          case '1-7':
            if (validity < 1 || validity > 7) return false;
            break;
          case '8-30':
            if (validity < 8 || validity > 30) return false;
            break;
          case '31-90':
            if (validity < 31 || validity > 90) return false;
            break;
          case '90+':
            if (validity <= 90) return false;
            break;
        }
      }

      // Search query filter
      if (state.filters.searchQuery) {
        const query = state.filters.searchQuery.toLowerCase();
        const searchableText = `${offer.title} ${offer.operator} ${offer.data_amount}`.toLowerCase();
        if (!searchableText.includes(query)) {
          return false;
        }
      }

      return true;
    });

    // Sort offers
    switch (state.filters.sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.selling_price - b.selling_price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.selling_price - a.selling_price);
        break;
      case 'data-high':
        filtered.sort((a, b) => {
          const aData = parseFloat(a.data_amount.replace(/[^\d.]/g, ''));
          const bData = parseFloat(b.data_amount.replace(/[^\d.]/g, ''));
          return bData - aData;
        });
        break;
      case 'validity-low':
        filtered.sort((a, b) => a.validity_days - b.validity_days);
        break;
      case 'validity-high':
        filtered.sort((a, b) => b.validity_days - a.validity_days);
        break;
      case 'newest':
      default:
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
    }

    return filtered;
  }, [offers, state.filters]);

  // Get favorite offers
  const favoriteOffers = useMemo(() => {
    if (!offers) return [];
    return offers.filter(offer => state.favorites.includes(offer.id));
  }, [offers, state.favorites]);

  // Get comparison offers
  const comparisonOffers = useMemo(() => {
    if (!offers) return [];
    return offers.filter(offer => state.comparison.includes(offer.id));
  }, [offers, state.comparison]);

  // Helper functions
  const isFavorite = (offerId: string) => state.favorites.includes(offerId);
  const isInComparison = (offerId: string) => state.comparison.includes(offerId);
  const canAddToComparison = state.comparison.length < 3;

  return {
    // Data
    offers: filteredOffers,
    favoriteOffers,
    comparisonOffers,
    loading,
    error,
    
    // State
    filters: state.filters,
    ui: state.ui,
    
    // Helper functions
    isFavorite,
    isInComparison,
    canAddToComparison,
    
    // Actions (these will be handled by context dispatch)
    refetch,
  };
}
